# 🏥 Website Đăng Ký Lịch Khám Bệnh

> **Hệ thống quản lý đặt lịch khám bệnh trực tuyến hiện đại với kiến trúc microservices và triển khai trên đám mây**

[![CI/CD Pipeline](https://github.com/your-username/CNPM_WebSiteDKKhamBenh/actions/workflows/ci-cd.yml/badge.svg)](https://github.com/your-username/CNPM_WebSiteDKKhamBenh/actions/workflows/ci-cd.yml)
[![Docker](https://img.shields.io/badge/Docker-Enabled-blue?logo=docker)](https://www.docker.com/)
[![Cloud Deployment](https://img.shields.io/badge/Cloud-GCP-green?logo=google-cloud)](https://cloud.google.com/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 📋 <PERSON>ục lụ<PERSON>

- [🎯 Giới thiệu về Công nghệ Phần mềm](#-giới-thiệu-về-công-nghệ-phần-mềm)
- [📊 Đặc tả Nhu cầu Ứng dụng](#-đặc-tả-nhu-cầu-ứng-dụng)
- [🔄 Mô hình Phát triển Phần mềm](#-mô-hình-phát-triển-phần-mềm)
- [📈 Quy trình Agile & Quản lý Dự án](#-quy-trình-agile--quản-lý-dự-án)
- [🏗️ Kiến trúc Hệ thống & Microservices](#️-kiến-trúc-hệ-thống--microservices)
- [☁️ Triển khai Đám mây & DevOps](#️-triển-khai-đám-mây--devops)
- [🔒 Bảo mật & An toàn Phần mềm](#-bảo-mật--an-toàn-phần-mềm)
- [🛠️ Công nghệ & Công cụ Phát triển](#️-công-nghệ--công-cụ-phát-triển)
- [🚀 Hướng dẫn Cài đặt & Triển khai](#-hướng-dẫn-cài-đặt--triển-khai)
- [🧪 Testing & Quality Assurance](#-testing--quality-assurance)
- [👥 Kỹ năng Làm việc Nhóm](#-kỹ-năng-làm-việc-nhóm)
- [📚 Tài liệu Tham khảo](#-tài-liệu-tham-khảo)

## 🎯 Giới thiệu về Công nghệ Phần mềm

### Khái niệm Cơ bản về Công nghệ Phần mềm

Công nghệ phần mềm là một ngành kỹ thuật tập trung vào việc phát triển, vận hành và bảo trì các hệ thống phần mềm một cách có hệ thống, kỷ luật và có thể đo lường được. Dự án này minh họa các khái niệm cốt lõi của công nghệ phần mềm hiện đại:

- **Kỹ thuật Phần mềm**: Áp dụng các nguyên tắc kỹ thuật để phát triển phần mềm chất lượng cao
- **Quản lý Dự án**: Sử dụng phương pháp Agile để quản lý tiến độ và chất lượng
- **Kiến trúc Phần mềm**: Thiết kế hệ thống với kiến trúc microservices và RESTful API
- **DevOps**: Tích hợp phát triển và vận hành thông qua CI/CD pipeline
- **Cloud Computing**: Triển khai ứng dụng trên nền tảng đám mây (GCP)

### Tính Cần thiết của Phần mềm Phân tán/Microservices/Cloud

Trong bối cảnh y tế hiện đại, việc số hóa quy trình đặt lịch khám bệnh đòi hỏi:

1. **Khả năng mở rộng (Scalability)**: Hệ thống cần xử lý hàng nghìn lượt đặt lịch đồng thời
2. **Tính sẵn sàng cao (High Availability)**: Dịch vụ y tế không thể gián đoạn
3. **Bảo mật dữ liệu**: Thông tin bệnh nhân cần được bảo vệ tuyệt đối
4. **Tích hợp đa nền tảng**: Kết nối với các hệ thống bệnh viện khác nhau
5. **Chi phí tối ưu**: Sử dụng tài nguyên đám mây hiệu quả

## 📊 Đặc tả Nhu cầu Ứng dụng

### Mô tả Tổng quan

Hệ thống Website Đăng Ký Lịch Khám Bệnh là một ứng dụng web toàn diện, được thiết kế để số hóa và tối ưu hóa quy trình đặt lịch khám bệnh tại các cơ sở y tế. Ứng dụng cung cấp giao diện thân thiện cho bệnh nhân, bác sĩ và quản trị viên.

### Các Chức năng Chính

#### 👤 Dành cho Bệnh nhân
- Đăng ký tài khoản và quản lý thông tin cá nhân
- Tìm kiếm bác sĩ theo chuyên khoa, địa điểm
- Xem lịch trống của bác sĩ theo thời gian thực
- Đặt lịch khám và nhận xác nhận qua email
- Theo dõi lịch sử khám bệnh và kết quả

#### 👨‍⚕️ Dành cho Bác sĩ
- Quản lý lịch làm việc và thời gian khám
- Xem danh sách bệnh nhân đã đặt lịch
- Cập nhật trạng thái cuộc hẹn
- Quản lý thông tin chuyên môn và kinh nghiệm

#### 🔧 Dành cho Quản trị viên
- Quản lý người dùng (bệnh nhân, bác sĩ)
- Quản lý chuyên khoa và dịch vụ y tế
- Theo dõi thống kê và báo cáo hệ thống
- Cấu hình các tham số hệ thống

### Yêu cầu Phi chức năng

- **Hiệu suất**: Thời gian phản hồi < 2 giây
- **Khả năng mở rộng**: Hỗ trợ 10,000+ người dùng đồng thời
- **Bảo mật**: Tuân thủ chuẩn HIPAA cho dữ liệu y tế
- **Tính sẵn sàng**: Uptime 99.9%
- **Tương thích**: Hỗ trợ đa nền tảng (Web, Mobile)

## 🔄 Mô hình Phát triển Phần mềm

### Waterfall vs Agile

Dự án này áp dụng **mô hình Agile** thay vì mô hình Waterfall truyền thống vì những lý do sau:

| Waterfall            | Agile (Được chọn)             |
| -------------------- | ----------------------------- |
| Tuần tự, cứng nhắc   | Linh hoạt, lặp lại            |
| Khó thay đổi yêu cầu | Thích ứng với thay đổi        |
| Test cuối dự án      | Test liên tục                 |
| Giao hàng cuối kỳ    | Giao hàng sớm và thường xuyên |

### Lý do Chọn Agile

1. **Thích ứng nhanh**: Y tế là lĩnh vực thay đổi nhanh, cần linh hoạt
2. **Phản hồi sớm**: Nhận feedback từ bác sĩ và bệnh nhân ngay từ đầu
3. **Giảm rủi ro**: Phát hiện và sửa lỗi sớm
4. **Chất lượng cao**: Test và review liên tục



## 🏗️ Kiến trúc Hệ thống & Microservices

### Tổng quan Kiến trúc

Hệ thống được thiết kế theo kiến trúc **3-tier monolithic** với frontend, backend và database tách biệt, triển khai bằng Docker containers:

```mermaid
graph TB
    %% Users
    Patient[👤 Bệnh nhân<br/>Web Browser]
    Doctor[👨‍⚕️ Bác sĩ<br/>Web Browser]
    Admin[👨‍💼 Quản trị viên<br/>Web Browser]

    %% Production Load Balancer (only in production)
    LB[⚖️ Nginx Load Balancer<br/>Production Only]

    %% Frontend Application
    Frontend[🖥️ Frontend Application<br/>Next.js 15 + React 18<br/>Port: 3000<br/>SSR + CSR]

    %% Backend Application (Monolithic)
    Backend[🔧 Backend API Server<br/>Express.js + Node.js 20<br/>Port: 8080<br/>Monolithic Architecture]

    %% API Routes (inside Backend)
    subgraph BackendRoutes[" Backend API Routes "]
        AuthAPI[🔐 /api/auth<br/>Login/Register]
        UserAPI[👥 /api/users<br/>User Management]
        DoctorAPI[👨‍⚕️ /api/doctor<br/>Doctor Profiles]
        SpecialtyAPI[🏥 /api/specialties<br/>Medical Specialties]
        ScheduleAPI[📅 /api/schedule<br/>Doctor Schedules]
        BookingAPI[📝 /api/bookings<br/>Appointments]
        AllcodeAPI[⚙️ /api/allcode<br/>System Codes]
        HealthAPI[💚 /api/health<br/>Health Check]
        SwaggerAPI[📚 /api-docs<br/>API Documentation]
    end

    %% Database
    MySQL[(🗄️ MySQL 8.0<br/>Database: DBDKKHAMBENH<br/>Port: 3306)]

    %% File Storage
    FileSystem[📁 File System<br/>/uploads/avatars<br/>Static Files]

    %% Development vs Production Flow
    Patient --> Frontend
    Doctor --> Frontend
    Admin --> Frontend

    %% Production has load balancer
    Frontend -.->|Production| LB
    LB -.->|Production| Backend

    %% Direct connection in development
    Frontend -->|Development<br/>http://localhost:8080/api| Backend

    %% Backend connects to database
    Backend --> MySQL
    Backend --> FileSystem

    %% API Routes are part of Backend
    Backend --> BackendRoutes

    %% Docker Containers (Development)
    subgraph DockerDev[" Docker Development Environment "]
        Frontend
        Backend
        MySQL
    end

    %% Docker Images (Production)
    subgraph DockerProd[" Docker Production Images "]
        ProdFrontend[📦 tongnguyen/frontend:latest]
        ProdBackend[📦 tongnguyen/backend:latest]
    end

    %% Styling
    classDef userClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef frontendClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef backendClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef dbClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef apiClass fill:#e0f2f1,stroke:#00695c,stroke-width:1px
    classDef dockerClass fill:#e1f5fe,stroke:#0277bd,stroke-width:2px

    class Patient,Doctor,Admin userClass
    class Frontend,LB frontendClass
    class Backend,BackendRoutes backendClass
    class MySQL,FileSystem dbClass
    class AuthAPI,UserAPI,DoctorAPI,SpecialtyAPI,ScheduleAPI,BookingAPI,AllcodeAPI,HealthAPI,SwaggerAPI apiClass
    class DockerDev,DockerProd,ProdFrontend,ProdBackend dockerClass
```

### Mô tả Chức năng từng Phần

#### 1. **Frontend Layer (Presentation Tier)**
- **Công nghệ**: Next.js 15, React 18, TypeScript
- **Port**: 3000
- **Chức năng**:
  - Server-Side Rendering (SSR) cho SEO tối ưu
  - Client-Side Rendering (CSR) cho UX mượt mà
  - Responsive design cho mobile/desktop
  - Static file serving từ `/public`
  - Image optimization với Next.js Image component

#### 2. **Backend Layer (Application Tier)**
- **Công nghệ**: Node.js 20, Express.js, Sequelize ORM
- **Port**: 8080
- **Kiến trúc**: Monolithic với MVC pattern
- **Chức năng**:
  - RESTful API endpoints
  - JWT Authentication & Authorization
  - File upload handling (multer)
  - CORS configuration
  - Swagger API documentation
  - Health check monitoring

#### 3. **API Endpoints (Monolithic Routes)**

##### Authentication Routes (`/api/auth`)
- **POST** `/login` - User authentication
- **POST** `/register` - User registration
- **POST** `/logout` - User logout

##### User Management (`/api/users`)
- **GET** `/` - Get all users
- **GET** `/:id` - Get user by ID
- **PUT** `/:id` - Update user profile
- **DELETE** `/:id` - Delete user

##### Doctor Management (`/api/doctor`)
- **GET** `/` - Get all doctors
- **GET** `/:id` - Get doctor details
- **PUT** `/:id` - Update doctor profile

##### Specialty Management (`/api/specialties`)
- **GET** `/` - Get all specialties
- **POST** `/` - Create specialty
- **PUT** `/:id` - Update specialty
- **DELETE** `/:id` - Delete specialty

##### Schedule Management (`/api/schedule`)
- **GET** `/` - Get all schedules
- **POST** `/` - Create schedule
- **PUT** `/:id` - Update schedule
- **DELETE** `/:id` - Delete schedule

##### Booking Management (`/api/bookings`)
- **GET** `/` - Get all bookings
- **POST** `/` - Create booking
- **GET** `/:id` - Get booking details
- **PUT** `/:id` - Update booking status

##### System Codes (`/api/allcode`)
- **GET** `/type` - Get codes by type
- **GET** `/types` - Get all types
- **POST** `/` - Create new code

#### 4. **Database Layer (Data Tier)**
- **Công nghệ**: MySQL 8.0 với Sequelize ORM
- **Database**: DBDKKHAMBENH
- **Port**: 3306 (3307 external)
- **Models**:
  - User (patients, doctors, admins)
  - Specialty (medical departments)
  - Schedule (doctor availability)
  - Booking (appointments)
  - Allcode (system configurations)
- **Features**:
  - Foreign key relationships
  - Data validation
  - Migrations & seeders
  - Connection pooling

### RESTful API Design

#### API Endpoints Overview

| Service         | Method | Endpoint             | Description         |
| --------------- | ------ | -------------------- | ------------------- |
| **Auth**        | POST   | `/api/auth/login`    | User authentication |
| **Auth**        | POST   | `/api/auth/register` | User registration   |
| **Users**       | GET    | `/api/users`         | Get all users       |
| **Users**       | GET    | `/api/users/:id`     | Get user by ID      |
| **Users**       | PUT    | `/api/users/:id`     | Update user         |
| **Doctors**     | GET    | `/api/doctor`        | Get all doctors     |
| **Doctors**     | GET    | `/api/doctor/:id`    | Get doctor details  |
| **Bookings**    | POST   | `/api/bookings`      | Create booking      |
| **Bookings**    | GET    | `/api/bookings/:id`  | Get booking details |
| **Schedules**   | GET    | `/api/schedule`      | Get schedules       |
| **Schedules**   | POST   | `/api/schedule`      | Create schedule     |
| **Specialties** | GET    | `/api/specialties`   | Get all specialties |

#### API Response Format

```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    // Response data
  },
  "meta": {
    "timestamp": "2024-01-15T10:30:00Z",
    "version": "1.0.0"
  }
}
```

### Cloud Architecture & Deployment

#### Deployment Strategy
- **Development**: Local Docker containers
- **Staging**: Google Cloud Platform (GCP)
- **Production**: GCP with auto-scaling

#### GCP Services Used
- **Compute Engine**: Application hosting
- **Cloud SQL**: Managed MySQL database
- **Cloud Storage**: File storage
- **Cloud CDN**: Content delivery
- **Cloud Load Balancing**: Traffic distribution

## ☁️ Triển khai Đám mây & DevOps

### CI/CD Pipeline

Dự án sử dụng **GitHub Actions** để tự động hóa quy trình phát triển và triển khai:

```mermaid
graph LR
    A[Code Push] --> B[GitHub Actions]
    B --> C[Run Tests]
    C --> D[Build Docker Images]
    D --> E[Push to Registry]
    E --> F[Deploy to GCP]
    F --> G[Health Check]
    G --> H[Notify Team]
```

#### Workflow Stages

1. **🧪 Testing Stage**
   - Frontend unit tests (Jest)
   - Backend unit tests (Jest + Supertest)
   - Integration tests với MySQL
   - Code coverage reports

2. **🏗️ Build Stage**
   - Docker image build cho frontend/backend
   - Multi-stage builds để tối ưu size
   - Cache layers để tăng tốc build

3. **🚀 Deploy Stage**
   - Automatic deployment to staging
   - Manual approval for production
   - Blue-green deployment strategy
   - Rollback capabilities

#### Environment Management

| Environment     | Branch    | Auto Deploy | URL                   |
| --------------- | --------- | ----------- | --------------------- |
| **Development** | `develop` | ✅ Yes       | `dev.example.com`     |
| **Staging**     | `main`    | ✅ Yes       | `staging.example.com` |
| **Production**  | `main`    | ❌ Manual    | `app.example.com`     |

### Docker Configuration

#### Multi-stage Dockerfile (Backend)
```dockerfile
# Build stage
FROM node:20-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

# Production stage
FROM node:20-alpine AS production
WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY . .
EXPOSE 8080
CMD ["npm", "start"]
```

#### Docker Compose Services
- **Frontend**: Next.js application (Port 3000)
- **Backend**: Express.js API (Port 8080)
- **Database**: MySQL 8.0 (Port 3306)
- **Nginx**: Reverse proxy & load balancer

## 🔒 Bảo mật & An toàn Phần mềm

### Security Measures Implemented

#### 1. **Authentication & Authorization**
- **JWT Tokens**: Stateless authentication
- **Role-based Access Control (RBAC)**:
  - `R1`: Patient role
  - `R2`: Doctor role
  - `R3`: Admin role
- **Password Security**:
  - bcrypt hashing (salt rounds: 12)
  - Password complexity requirements
  - Account lockout after failed attempts

#### 2. **Data Protection**
- **Encryption at Rest**: Database encryption
- **Encryption in Transit**: HTTPS/TLS 1.3
- **Data Anonymization**: PII masking in logs
- **GDPR Compliance**: Data retention policies

#### 3. **API Security**
- **Rate Limiting**: 100 requests/minute per IP
- **CORS Configuration**: Restricted origins
- **Input Validation**: Joi schema validation
- **SQL Injection Prevention**: Parameterized queries
- **XSS Protection**: Content Security Policy

#### 4. **Infrastructure Security**
- **Container Security**: Non-root user execution
- **Network Security**: Private subnets, firewalls
- **Secrets Management**: Environment variables
- **Monitoring**: Real-time security alerts

#### 5. **HIPAA Compliance** (Healthcare Data)
- **Access Controls**: Minimum necessary access
- **Audit Logging**: All data access logged
- **Data Backup**: Encrypted backups
- **Incident Response**: Security breach procedures

### Security Testing

```bash
# Security audit
npm audit

# Dependency vulnerability scan
npm run security:scan

# OWASP ZAP security testing
npm run security:test
```

## 🛠️ Công nghệ & Công cụ Phát triển

### Technology Stack

#### Frontend
- **Framework**: Next.js 15 (React 18)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **State Management**: React Hooks
- **Form Handling**: React Hook Form
- **HTTP Client**: Axios
- **Testing**: Jest, React Testing Library

#### Backend
- **Runtime**: Node.js 20
- **Framework**: Express.js
- **Language**: JavaScript (ES6+)
- **Database**: MySQL 8.0
- **ORM**: Sequelize
- **Authentication**: JWT
- **Documentation**: Swagger/OpenAPI

#### DevOps & Tools
- **Version Control**: Git, GitHub
- **Containerization**: Docker, Docker Compose
- **CI/CD**: GitHub Actions
- **Cloud Platform**: Google Cloud Platform (GCP)
- **Monitoring**: Cloud Monitoring
- **Package Manager**: npm

### Development Tools

#### Code Quality
- **Linting**: ESLint (disabled in CI as per preference)
- **Formatting**: Prettier
- **Type Checking**: TypeScript
- **Testing**: Jest, Supertest
- **Coverage**: Istanbul/nyc

#### Git Workflow
```bash
# Feature development
git checkout -b feature/booking-system
git add .
git commit -m "feat: implement booking system"
git push origin feature/booking-system

# Create Pull Request
# Code review & merge to develop
# Auto-deploy to staging
```

#### Local Development Setup
```bash
# Clone repository
git clone https://github.com/your-username/CNPM_WebSiteDKKhamBenh.git
cd CNPM_WebSiteDKKhamBenh

# Install dependencies
npm run install:deps

# Start development environment
npm run dev:docker
```

## 🚀 Hướng dẫn Cài đặt & Triển khai

### Yêu cầu Hệ thống

- **Node.js**: v20.x hoặc cao hơn
- **npm**: v8.x hoặc cao hơn
- **Docker**: v20.x hoặc cao hơn
- **Docker Compose**: v2.x hoặc cao hơn
- **MySQL**: v8.0 hoặc cao hơn (nếu chạy local)

### Cài đặt Local Development

#### Option 1: Docker Compose (Khuyến nghị)

```bash
# Clone repository
git clone https://github.com/your-username/CNPM_WebSiteDKKhamBenh.git
cd CNPM_WebSiteDKKhamBenh

# Copy environment file
cp .env.local .env

# Start all services
npm run dev:docker

# Health check
curl http://localhost:8080/api/health
```

#### Option 2: Native Development

```bash
# Install dependencies
npm run install:deps

# Start database (Docker)
docker-compose up db-mysql -d

# Start backend
cd backend && npm run dev

# Start frontend (new terminal)
cd frontend && npm run dev
```

### Cài đặt Production

#### GCP Deployment

```bash
# Build and push Docker images
docker build -t gcr.io/your-project/frontend ./frontend
docker build -t gcr.io/your-project/backend ./backend
docker push gcr.io/your-project/frontend
docker push gcr.io/your-project/backend

# Deploy using gcloud
gcloud run deploy frontend --image gcr.io/your-project/frontend
gcloud run deploy backend --image gcr.io/your-project/backend
```

### Environment Configuration

#### Development (.env.local)
```bash
NODE_ENV=development
FRONTEND_PORT=3000
BACKEND_PORT=8080
DB_HOST=localhost
DB_PORT=3307
DB_NAME=DBDKKHAMBENH
DB_USER=root
DB_PASSWORD=123456
JWT_SECRET=your-dev-secret
NEXT_PUBLIC_API_URL=http://localhost:8080/api/
```

#### Production (.env.production)
```bash
NODE_ENV=production
DB_HOST=your-cloud-sql-host
DB_NAME=production_db
DB_USER=production_user
DB_PASSWORD=secure-password
JWT_SECRET=super-secure-secret
NEXT_PUBLIC_API_URL=https://api.yourdomain.com/api/
```

## 🧪 Testing & Quality Assurance

### Testing Strategy

#### Unit Testing
```bash
# Frontend tests
cd frontend && npm run test:unit

# Backend tests
cd backend && npm run test:unit

# All tests
npm run test
```

#### Integration Testing
```bash
# Start test database
docker-compose -f docker-compose.test.yml up -d

# Run integration tests
npm run test:integration

# Cleanup
docker-compose -f docker-compose.test.yml down
```

#### End-to-End Testing
```bash
# Start application
npm run dev:docker

# Run E2E tests
cd frontend && npm run test:e2e

# Generate reports
npm run test:e2e:report
```

### Code Coverage

```bash
# Generate coverage reports
npm run test:coverage

# View coverage report
open coverage/lcov-report/index.html
```

**Current Coverage**:
- Frontend: 85% lines, 80% branches
- Backend: 90% lines, 85% branches

### Quality Gates

- ✅ Unit test coverage > 80%
- ✅ Integration test coverage > 70%
- ✅ No critical security vulnerabilities
- ✅ Performance budget < 3s load time
- ✅ Accessibility score > 90%

## 👥 Kỹ năng Làm việc Nhóm

### Team Collaboration

#### Communication Tools
- **Daily Standups**: 9:00 AM (15 phút)
- **Sprint Planning**: Thứ 2 đầu sprint (2 giờ)
- **Sprint Review**: Thứ 6 cuối sprint (1 giờ)
- **Retrospective**: Thứ 6 cuối sprint (1 giờ)

#### Code Review Process
1. **Feature Branch**: Tạo branch từ `develop`
2. **Development**: Implement feature với tests
3. **Pull Request**: Tạo PR với description chi tiết
4. **Review**: Ít nhất 2 reviewers approve
5. **Merge**: Squash merge vào `develop`

#### Documentation Standards
- **Code Comments**: JSDoc cho functions
- **API Documentation**: Swagger/OpenAPI
- **Architecture Decisions**: ADR documents
- **User Stories**: Acceptance criteria rõ ràng

### Presentation Skills

#### Sprint Demo Format
1. **Context** (2 phút): Mục tiêu sprint
2. **Demo** (10 phút): Live demonstration
3. **Metrics** (3 phút): Performance, coverage
4. **Q&A** (5 phút): Stakeholder feedback

#### Technical Presentation
- **Problem Statement**: Vấn đề cần giải quyết
- **Solution Architecture**: Thiết kế kỹ thuật
- **Implementation**: Code walkthrough
- **Results**: Metrics và outcomes

## 📁 Cấu trúc Dự án

```
CNPM_WebSiteDKKhamBenh/
├── 📁 .github/                 # GitHub workflows & templates
│   ├── workflows/              # CI/CD pipelines
│   └── ISSUE_TEMPLATE/         # Issue templates
├── 📁 frontend/                # Next.js application
│   ├── src/
│   │   ├── components/         # Reusable UI components
│   │   ├── pages/             # Next.js pages
│   │   ├── hooks/             # Custom React hooks
│   │   ├── utils/             # Utility functions
│   │   └── types/             # TypeScript definitions
│   ├── public/                # Static assets
│   ├── __tests__/             # Frontend tests
│   └── Dockerfile             # Frontend container
├── 📁 backend/                 # Express.js API
│   ├── src/
│   │   ├── controllers/       # Route handlers
│   │   ├── models/            # Sequelize models
│   │   ├── routes/            # API routes
│   │   ├── middleware/        # Custom middleware
│   │   ├── config/            # Configuration files
│   │   └── utils/             # Utility functions
│   ├── __tests__/             # Backend tests
│   └── Dockerfile             # Backend container
├── 📁 deploy/                  # Deployment configurations
│   ├── docker-compose.prod.yml
│   ├── nginx.conf
│   └── deploy.sh
├── 📁 scripts/                 # Development scripts
│   ├── dev-docker.sh
│   ├── test-all.sh
│   └── cleanup-git-history.ps1
├── 📁 docs/                    # Project documentation
│   ├── api/                   # API documentation
│   ├── architecture/          # Architecture diagrams
│   └── user-guide/            # User manuals
├── docker-compose.yml          # Local development
├── .env.example               # Environment template
├── package.json               # Root package configuration
└── README.md                  # This file
```

## 📚 Tài liệu Tham khảo

### Software Engineering Concepts
- [Software Engineering - Ian Sommerville](https://www.pearson.com/us/higher-education/program/Sommerville-Software-Engineering-10th-Edition/PGM58925.html)
- [Clean Code - Robert Martin](https://www.oreilly.com/library/view/clean-code-a/9780136083238/)
- [Design Patterns - Gang of Four](https://www.oreilly.com/library/view/design-patterns-elements/0201633612/)

### Agile & Project Management
- [Scrum Guide](https://scrumguides.org/)
- [Agile Manifesto](https://agilemanifesto.org/)
- [User Story Mapping - Jeff Patton](https://www.oreilly.com/library/view/user-story-mapping/9781491904893/)

### Cloud & Microservices
- [Building Microservices - Sam Newman](https://www.oreilly.com/library/view/building-microservices-2nd/9781492034018/)
- [Cloud Native Patterns - Cornelia Davis](https://www.manning.com/books/cloud-native-patterns)
- [Google Cloud Documentation](https://cloud.google.com/docs)

### Security & Privacy
- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [HIPAA Compliance Guide](https://www.hhs.gov/hipaa/index.html)
- [Web Security - MDN](https://developer.mozilla.org/en-US/docs/Web/Security)

### Technical Documentation
- [API Documentation](http://localhost:8080/api-docs) (Local)
- [Architecture Decision Records](./docs/architecture/README.md)
- [Database Schema](./docs/database/schema.md)
- [Deployment Guide](./DOCKER_GUIDE.md)

---

## 📞 Liên hệ & Hỗ trợ

- **Project Lead**: [Tên Lead] - <EMAIL>
- **Technical Issues**: [GitHub Issues](https://github.com/your-username/CNPM_WebSiteDKKhamBenh/issues)
- **Documentation**: [Wiki](https://github.com/your-username/CNPM_WebSiteDKKhamBenh/wiki)
- **Live Demo**: [https://cnpm-demo.example.com](https://cnpm-demo.example.com)

---

<div align="center">

**🏥 Website Đăng Ký Lịch Khám Bệnh**

*Được phát triển với ❤️ bởi CNPM Team*

[![GitHub stars](https://img.shields.io/github/stars/your-username/CNPM_WebSiteDKKhamBenh?style=social)](https://github.com/your-username/CNPM_WebSiteDKKhamBenh/stargazers)
[![GitHub forks](https://img.shields.io/github/forks/your-username/CNPM_WebSiteDKKhamBenh?style=social)](https://github.com/your-username/CNPM_WebSiteDKKhamBenh/network/members)

</div>
